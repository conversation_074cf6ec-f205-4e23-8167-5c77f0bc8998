import os
#os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 手动指定使用的GPU，通常由 accelerate 自动管理
import os.path as osp
import json
import torch
import pickle
import logging
import numpy as np
from tqdm import tqdm
from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed, ProjectConfiguration, DeepSpeedPlugin # DeepSpeedPlugin 未在此配置中使用
from model import SimVP # 假设 SimVP 模型定义在此
from utils import *     # 假设包含 print_log, output_namespace, check_dir, load_data, metric 等辅助函数
from API import *       # 可能包含其他API或 metric, load_data (注意潜在的命名冲突)

# 脚本开始时尝试清空CUDA缓存，可能有助于在某些情况下减少显存碎片
torch.cuda.empty_cache

class Exp:
    def __init__(self, args):
        super(Exp, self).__init__()
        self.args = args  # 保存命令行参数
        self.config = self.args.__dict__ # 将参数转为字典，方便访问

        # 设置全局随机种子，确保实验的可复现性
        set_seed(self.args.seed)
                # 构建实验主输出目录路径 (例如：./results/experiment_name)
        self.path = osp.join(self.args.res_dir, self.args.ex_name)
        check_dir(self.path) # 确保目录存在，不存在则创建

        # 构建检查点保存目录路径 (例如：./results/experiment_name/checkpoints)
        self.checkpoints_path = osp.join(self.path, 'checkpoints')
        check_dir(self.checkpoints_path) # 确保目录存在

        self.log_path = osp.join(self.path, 'logs')
        check_dir(self.log_path)
        
        # 初始化 Accelerator，用于简化分布式训练和混合精度等设置
        # 配置DDP插件，find_unused_parameters=False 可以提升性能，但需确保模型所有参数都参与梯度计算
        ddp_plugin = DistributedDataParallelKwargs(find_unused_parameters=False)
        # 配置项目目录，用于 accelerate 管理输出（如日志、检查点）
        project_config = ProjectConfiguration(project_dir=self.args.res_dir,
                                              logging_dir=self.log_path)
        # DeepSpeed 插件示例，当前未启用
        # deepspeed_plugin = DeepSpeedPlugin(zero_stage=2,gradient_accumulation_steps=1)

        self.accelerator = Accelerator(
            split_batches=True,  # 在DDP中，有助于更均匀地分配批次到各设备
            mixed_precision=self.args.mixed_precision if hasattr(args, 'mixed_precision') else 'no', # 从参数配置混合精度
            project_config=project_config, # 应用项目配置
            gradient_accumulation_steps=self.args.gradient_accumulation_steps, # 从参数配置梯度累积步数 (重要：确保这里不是硬编码为1)
            kwargs_handlers=[ddp_plugin] # 应用DDP特定参数
        )
        # 获取当前进程分配到的设备 (CPU 或 GPU)
        self.device = self.accelerator.device
        # 准备实验目录和日志
        self._preparation()
        # 仅在主进程打印实验配置和 accelerate 的状态信息
        if self.accelerator.is_main_process:
            print_log(output_namespace(self.args)) # 打印所有参数
            print_log(f"Using mixed precision: {self.args.mixed_precision if hasattr(args, 'mixed_precision') else 'no'}")
            print_log(f"Number of processes: {self.accelerator.num_processes}")
            print_log(f"Distributed type: {self.accelerator.distributed_type}")

        # 加载数据、构建模型、选择优化器和损失函数
        self._get_data()
        self._build_model()
        self._select_optimizer()
        self._select_criterion()

        # 初始化用于跟踪最佳验证损失的变量
        self.best_val_loss = float('inf')

        # 使用 accelerate.prepare() 包装所有核心组件，使其适应分布式和混合精度环境
        # prepare 会自动处理模型并行化 (如DDP)、数据加载器分发、优化器和调度器包装等
        self.model, self.optimizer, self.scheduler, self.train_loader, self.vali_loader, self.test_loader = self.accelerator.prepare(
            self.model, self.optimizer, self.scheduler, self.train_loader, self.vali_loader, self.test_loader
        )

    def _preparation(self):

        # 定义参数保存文件路径
        sv_param = osp.join(self.path, 'model_param.json')
        # 仅主进程执行文件IO操作，避免冲突
        if self.accelerator.is_main_process:
            # 将实验参数以JSON格式保存
            with open(sv_param, 'w') as file_obj:
                json.dump(self.args.__dict__, file_obj)

            # 配置日志记录
            # 清除已存在的日志处理器，避免重复记录
            for handler in logging.root.handlers[:]:
                logging.root.removeHandler(handler)
            # 设置日志级别、输出文件、追加模式和格式
            logging.basicConfig(level=logging.INFO, filename=osp.join(self.log_path, 'log.log'),
                                filemode='a', format='%(asctime)s - %(message)s')

    def _build_model(self):
        args = self.args
        # 实例化 SimVP 模型
        self.model = SimVP(tuple(args.in_shape), args.hid_S, args.hid_T, args.N_S, args.N_T)

    def _get_data(self):
        config = self.args.__dict__
        # 调用 load_data 函数加载训练、验证、测试数据加载器以及数据的均值和标准差
        # DataLoader 对象本身不占用GPU显存，数据在迭代时才加载到GPU
        self.train_loader, self.vali_loader, self.test_loader, self.data_mean, self.data_std = load_data(**config)
        # 如果验证加载器未定义，则使用测试加载器作为验证加载器
        self.vali_loader = self.test_loader if self.vali_loader is None else self.vali_loader

    def _select_optimizer(self):
        # 创建 Adam 优化器
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.args.lr)
        # 创建 OneCycleLR 学习率调度器
        # steps_per_epoch 基于每个进程的训练加载器长度
        self.scheduler = torch.optim.lr_scheduler.OneCycleLR(
            self.optimizer, max_lr=self.args.lr, steps_per_epoch=len(self.train_loader), epochs=self.args.epochs
        )

    def _select_criterion(self):
        # 选择均方误差作为损失函数
        self.criterion = torch.nn.MSELoss() # 默认 reduction='mean'，输出标量损失

    def _save(self, name=''):
        # 仅主进程执行保存操作
        if not self.accelerator.is_main_process:
            return
        # 保存模型权重 (.pth 文件)
        # accelerator.get_state_dict() 用于正确获取被包装模型的state_dict
        torch.save(self.accelerator.get_state_dict(self.model), osp.join(self.checkpoints_path, name + '.pth'))
        # 保存学习率调度器的状态 (.pkl 文件)，用于恢复训练时的学习率变化
        state = self.scheduler.state_dict()
        with open(osp.join(self.checkpoints_path, name + '.pkl'), 'wb') as fw:
            pickle.dump(state, fw)

    def _load(self, path):
        """在分布式环境中正确加载模型权重"""
        # 检查模型文件是否存在
        if not os.path.exists(path):
            # 仅主进程打印警告信息
            if self.accelerator.is_main_process:
                print_log(f"Warning: 模型文件 {path} 不存在，跳过加载")
            return

        # 解包模型，获取原始的 nn.Module 对象
        # unwrapped_model 是 self.model 内部原始模型的引用
        unwrapped_model = self.accelerator.unwrap_model(self.model)

        # 加载权重到解包后的模型中
        # map_location=self.device 确保权重加载到当前进程对应的设备
        # 修改 unwrapped_model 的权重会直接影响 self.model (DDP包装器) 内部模型的权重
        unwrapped_model.load_state_dict(torch.load(path, map_location=self.device))

        # 显式地用加载了新权重的 unwrapped_model 重新准备模型
        # self.model 被重新赋值为一个新的、由 accelerator.prepare 包装的对象
        # 这是一种明确且在某些复杂情况下更安全的做法，确保 accelerate 正确管理更新后的模型
        self.model = self.accelerator.prepare(unwrapped_model)

    def train(self, args):
        config = args.__dict__
        # recorder = Recorder(verbose=True) # Recorder 逻辑已整合到 train 方法中

        self.model.train()  # 设置模型为训练模式
        for epoch in range(config['epochs']): # 遍历所有 epoch
            train_loss = [] # 用于存储当前 epoch 每个进程的批次损失
            # 使用 tqdm 创建进度条，仅在主进程显示
            train_pbar = tqdm(self.train_loader, disable=not self.accelerator.is_main_process)

            for batch_x, batch_y in train_pbar: # 遍历训练数据加载器
                self.optimizer.zero_grad() # 清除之前的梯度

                # 使用 accelerate.autocast() 进行混合精度前向传播
                with self.accelerator.autocast():
                    pred_y = self.model(batch_x) # 模型预测
                    loss = self.criterion(pred_y, batch_y) # 计算损失 (标量)

                # 使用 accelerator.backward() 执行反向传播
                # 自动处理混合精度的损失缩放和分布式环境下的梯度同步（如果需要）
                self.accelerator.backward(loss)

                # 可选的同步点，确保所有进程都完成了反向传播，在梯度裁剪前是个好习惯
                self.accelerator.wait_for_everyone()

                # 仅在梯度需要同步时（即梯度累积的最后一步，或不使用累积时每一步）执行梯度裁剪
                if self.accelerator.sync_gradients:
                    clip_value = self.args.gradient_clipping if hasattr(self.args, 'gradient_clipping') else 1.0
                    self.accelerator.clip_grad_norm_(self.model.parameters(), clip_value)

                # 执行优化器步骤和学习率调度器步骤
                # accelerate 会在内部处理梯度累积，确保只在累积完成后实际更新参数
                self.optimizer.step()
                if not self.accelerator.optimizer_step_was_skipped:
                    self.scheduler.step()

                # 记录当前小批次的损失 (Python float)
                train_loss.append(loss.item())
                # 更新 tqdm 进度条的描述，显示当前批次损失 (仅主进程可见)
                train_pbar.set_description('train loss: {:.4f}'.format(loss.item()))

            # 计算当前 epoch 当前进程的平均训练损失
            local_train_loss = np.average(train_loss) if train_loss else 0.0
            # 使用 accelerator.gather() 收集所有进程的 local_train_loss，并计算全局平均训练损失
            gathered_train_loss = self.accelerator.gather(torch.tensor(local_train_loss, device=self.device)).mean().item()

            # 每隔 args.log_step 个 epoch 执行一次验证
            if epoch % args.log_step == 0: # 注意：第0个epoch也会验证
                with torch.no_grad(): # 验证时不需要计算梯度
                    vali_loss = self.vali(self.vali_loader) # 执行验证，获取验证损失
                    # 仅主进程执行定期快照保存
                    if self.accelerator.is_main_process:
                        # 可选：避免在epoch 0重复保存，如果checkpoint.pth也可能在epoch 0保存
                        if epoch > 0 and epoch % (args.log_step * 100) == 0:
                            self._save(name=str(epoch)) # 保存以epoch号命名的快照
                # 仅主进程打印日志和保存最佳模型
                if self.accelerator.is_main_process:
                    print_log("Epoch: {0} | Train Loss: {1:.4f} Vali Loss: {2:.4f}\n".format(
                        epoch + 1, gathered_train_loss, vali_loss))

                    # 检查当前验证损失是否优于已记录的最佳损失
                    if vali_loss < self.best_val_loss:
                        self.best_val_loss = vali_loss # 更新最佳损失
                        print_log(f"New best validation loss: {self.best_val_loss:.6f}. Saving checkpoint...")
                        self._save(name='checkpoint') # 保存为 'checkpoint.pth'

        # 训练结束后，所有进程等待，确保主进程可能进行的最后一次保存已完成且文件可见
        self.accelerator.wait_for_everyone()
        # 定义最佳模型检查点路径
        best_model_path = osp.join(self.checkpoints_path, 'checkpoint.pth')
        if self.accelerator.is_main_process:
            print_log(f"加载最佳模型: {best_model_path}")

        # 检查最佳模型文件是否存在，然后加载
        if osp.exists(best_model_path):
            self._load(best_model_path) # 所有进程加载最佳模型权重
        else:
            # 如果最佳模型文件不存在，主进程打印警告
            # 模型将保持训练最后一个epoch的状态
            if self.accelerator.is_main_process:
                print_log(f"Warning: 最佳模型文件 {best_model_path} 未找到。将使用最后一个epoch的模型进行测试。")

        # 所有进程等待，确保所有进程都完成了加载（或跳过加载）操作，同步进入测试阶段
        self.accelerator.wait_for_everyone()

    @torch.no_grad() # 测试时不需要计算梯度
    def test(self, args):
        """测试函数，用于在训练后评估模型性能"""
        self.model.eval() # 设置模型为评估模式

        # 使用 tqdm 创建进度条，仅在主进程显示
        test_pbar = tqdm(self.test_loader, disable=not self.accelerator.is_main_process)
        # 初始化用于累积指标的变量
        mse_total, mae_total, ssim_total, psnr_total = 0.0, 0.0, 0.0, 0.0
        num_samples = 0 # 记录总样本数

        for batch_x, batch_y in test_pbar: # 遍历测试数据
            # 使用 accelerate.autocast() 进行混合精度推理
            with self.accelerator.autocast():
                pred_y = self.model(batch_x) # 模型预测

            # 使用 accelerator.gather() 收集所有进程的预测结果和真实标签
            # 这样主进程可以获得完整的测试集结果进行评估
            gathered_pred = self.accelerator.gather(pred_y)
            gathered_batch_y = self.accelerator.gather(batch_y)

            # 仅主进程计算和累积评估指标
            if self.accelerator.is_main_process:
                # 将收集到的Tensor转为NumPy数组，并移到CPU
                pred_np = gathered_pred.detach().cpu().numpy()
                batch_y_np = gathered_batch_y.detach().cpu().numpy()

                # 调用 metric 函数计算评估指标
                # 假设 self.test_loader.dataset 有 mean 和 std 属性用于反归一化
                mse, mae, ssim, psnr = metric(pred_np, batch_y_np,
                                             self.test_loader.dataset.mean,
                                             self.test_loader.dataset.std,
                                             True) # True 可能表示进行反归一化

                actual_batch_size = pred_np.shape[0] # 获取实际的全局批次大小
                # 累积指标 (乘以批次大小，后续计算加权平均)
                mse_total += mse * actual_batch_size
                mae_total += mae * actual_batch_size
                ssim_total += ssim * actual_batch_size
                psnr_total += psnr * actual_batch_size
                num_samples += actual_batch_size

        # 仅主进程计算并打印最终的平均指标
        if self.accelerator.is_main_process and num_samples > 0:
            mse_avg = mse_total / num_samples
            mae_avg = mae_total / num_samples
            ssim_avg = ssim_total / num_samples
            psnr_avg = psnr_total / num_samples
            print_log('Test metrics:')
            print_log('mse:{:.4f}, mae:{:.4f}, ssim:{:.4f}, psnr:{:.4f}'.format(
                mse_avg, mae_avg, ssim_avg, psnr_avg))
            return mse_avg # 返回平均MSE

        return 0.0 # 如果不是主进程或没有样本，返回0

    @torch.no_grad() # 验证时不需要计算梯度
    def vali(self, vali_loader):
        self.model.eval() # 设置模型为评估模式
        total_loss = [] # 存储当前进程的批次损失
        # 使用 tqdm 创建进度条，仅在主进程显示
        vali_pbar = tqdm(vali_loader, disable=not self.accelerator.is_main_process)

        # 初始化用于累积指标的变量 (同test方法)
        mse_total, mae_total, ssim_total, psnr_total = 0.0, 0.0, 0.0, 0.0
        num_samples = 0

        for i, (batch_x, batch_y) in enumerate(vali_pbar): # 遍历验证数据
            # 提前终止验证，只评估约1000个样本，以节省时间
            if i * batch_x.shape[0] > 1000: # batch_x.shape[0] 是当前进程的批次大小
                break

            # 使用 accelerate.autocast() 进行混合精度推理
            with self.accelerator.autocast():
                pred_y = self.model(batch_x) # 模型预测

            loss = self.criterion(pred_y, batch_y) # 计算损失 (标量)
            total_loss.append(loss.item()) # 直接 .item()，因为loss已是标量
            # 更新 tqdm 进度条描述 (仅主进程可见)
            vali_pbar.set_description('vali loss: {:.4f}'.format(loss.item()))

            # 收集所有进程的预测结果和真实标签
            gathered_pred = self.accelerator.gather(pred_y)
            gathered_batch_y = self.accelerator.gather(batch_y)

            # 仅主进程计算和累积评估指标
            if self.accelerator.is_main_process:
                pred_np = gathered_pred.detach().cpu().numpy()
                batch_y_np = gathered_batch_y.detach().cpu().numpy()

                mse, mae, ssim, psnr = metric(pred_np, batch_y_np,
                                             self.vali_loader.dataset.mean, # 假设 vali_loader.dataset 也有 mean/std
                                             self.vali_loader.dataset.std,
                                             True)

                actual_batch_size = pred_np.shape[0]
                mse_total += mse * actual_batch_size
                mae_total += mae * actual_batch_size
                ssim_total += ssim * actual_batch_size
                psnr_total += psnr * actual_batch_size
                num_samples += actual_batch_size

        # 计算当前进程的平均验证损失
        local_total_loss = np.average(total_loss) if total_loss else 0.0
        # 使用 accelerator.gather() 收集所有进程的 local_total_loss，并计算全局平均验证损失
        gathered_total_loss = self.accelerator.gather(torch.tensor(local_total_loss, device=self.device)).mean().item()

        # 仅主进程计算并打印最终的平均验证指标
        if self.accelerator.is_main_process and num_samples > 0:
            mse_avg = mse_total / num_samples
            mae_avg = mae_total / num_samples
            ssim_avg = ssim_total / num_samples
            psnr_avg = psnr_total / num_samples
            print_log('vali mse:{:.4f}, mae:{:.4f}, ssim:{:.4f}, psnr:{:.4f}'.format(
                mse_avg, mae_avg, ssim_avg, psnr_avg))

        self.model.train()  # 验证结束后，恢复模型为训练模式
        return gathered_total_loss # 返回全局平均验证损失